import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const importProduct = mutation({
  args: {
    product: v.object({
      title: v.string(),
      description: v.string(),
      curationNotes: v.string(),
      supplierId: v.string(), // This is the supplier name
      priceInYuan: v.number(),
      serviceFee: v.number(),
      finalPrice: v.number(),
      tags: v.array(v.string()),
      images: v.array(v.string()),
      imageEmbedding: v.optional(v.null()),
      stockCount: v.number(),
      status: v.union(v.literal("active"), v.literal("inactive")),
      providerData: v.object({
        source: v.string(),
        productUrl: v.string(),
        providerId: v.string(),
        lastScraped: v.number(),
        providerSpecificData: v.optional(v.null()),
      }),
      pricingTiers: v.array(v.object({
        minQuantity: v.number(),
        maxQuantity: v.optional(v.null()),
        price: v.number(),
        currency: v.string(),
        discountPercentage: v.optional(v.null()),
      })),
      variants: v.array(v.object({
        type: v.string(),
        name: v.string(),
        value: v.string(),
        priceType: v.string(),
        absolutePrice: v.optional(v.union(v.number(), v.null())),
        priceModifier: v.number(),
        currency: v.optional(v.union(v.string(), v.null())),
        availableQuantity: v.optional(v.union(v.number(), v.null())),
        images: v.array(v.string()),
      })),
      customServices: v.array(v.object({
        name: v.string(),
        description: v.string(),
        minQuantity: v.number(),
        price: v.number(),
        currency: v.string(),
        isRequired: v.boolean(),
      })),
      attributes: v.array(v.object({
        name: v.string(),
        value: v.any(),
      })), // Pre-transformed attributes array
      createdBy: v.string(),
      updatedBy: v.string(),
    })
  },
  handler: async (ctx, { product }) => {
    // Check if product with the same URL already exists
    const existingProduct = await ctx.db
      .query("products")
      .withIndex("by_provider_product_url", (q) =>
        q.eq("providerData.productUrl", product.providerData.productUrl)
      )
      .first();

    if (existingProduct) {
      console.log(`Product with URL ${product.providerData.productUrl} already exists. Skipping.`);
      return; // Skip insertion
    }

    // 1. Find or create the supplier
    let supplierId;
    const supplierName = product.supplierId;

    const existingSupplier = await ctx.db
      .query("suppliers")
      .filter((q) => q.eq(q.field("name"), supplierName))
      .first();

    // 2. Get a user ID for createdBy/updatedBy
    // We'll find the first super_admin and use their user ID.
    const adminUser = await ctx.db
      .query("adminUsers")
      .filter((q) => q.eq(q.field("role"), "super_admin"))
      .first();

    if (!adminUser) {
      throw new Error(
        "Could not find a 'super_admin' user to assign as creator. Please create an admin user first."
      );
    }
    const userId = adminUser.userId;

    if (existingSupplier) {
      supplierId = existingSupplier._id;
    } else {
      supplierId = await ctx.db.insert("suppliers", {
        name: supplierName,
        isActive: true,
        createdBy: userId,
        contactInfo: {},
      });
    }

    console.log(`Processing product: ${product.title}`);
    console.log(`Attributes count: ${product.attributes.length}`);

    // 4. Transform data to match schema expectations (convert null to undefined)
    const transformedPricingTiers = product.pricingTiers.map(tier => ({
      minQuantity: tier.minQuantity,
      maxQuantity: tier.maxQuantity === null ? undefined : tier.maxQuantity,
      price: tier.price,
      currency: tier.currency,
      discountPercentage: tier.discountPercentage === null ? undefined : tier.discountPercentage,
    }));

    const transformedVariants = product.variants.map(variant => ({
      type: variant.type,
      name: variant.name,
      value: variant.value,
      priceType: variant.priceType as "modifier" | "absolute",
      absolutePrice: variant.absolutePrice === null ? undefined : variant.absolutePrice,
      priceModifier: variant.priceModifier === null ? undefined : variant.priceModifier,
      currency: variant.currency === null ? undefined : variant.currency,
      availableQuantity: variant.availableQuantity === null ? undefined : variant.availableQuantity,
      images: variant.images === null ? undefined : variant.images,
    }));

    // 5. Prepare the product for insertion
    const { supplierId: _, attributes: __, ...productData } = product;

    // 6. Insert the product
    await ctx.db.insert("products", {
      ...productData,
      pricingTiers: transformedPricingTiers,
      variants: transformedVariants,
      attributes: product.attributes,
      supplierId: supplierId,
      createdBy: userId,
      updatedBy: userId,
    });
  },
});
