"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ProductStatus } from "@/app/productRedesignMockData";

interface Supplier {
  _id: string;
  name: string;
  contactInfo: {
    email: string;
    phone: string;
  };
  platformUrl?: string;
  isActive: boolean;
}

interface ProductInfoFormProps {
  title: string;
  description: string;
  curationNotes: string;
  supplierId: string;
  status: ProductStatus;
  stockCount: number;
  suppliers: Supplier[];
  onFieldChange: (field: string, value: unknown) => void;
  errors?: Record<string, string>;
}

export function ProductInfoForm({
  title,
  description,
  curationNotes,
  supplierId,
  status,
  stockCount,
  suppliers,
  onFieldChange,
  errors = {}
}: ProductInfoFormProps) {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Product Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Product Title *
            </label>
            <Input
              value={title}
              onChange={(e) => onFieldChange("title", e.target.value)}
              placeholder="Enter a compelling product title"
              className={errors.title ? "border-destructive" : ""}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Description *
            </label>
            <textarea
              value={description}
              onChange={(e) => onFieldChange("description", e.target.value)}
              placeholder="Describe the product features, benefits, and specifications"
              className={`w-full px-3 py-2 border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none ${
                errors.description ? "border-destructive" : "border-input"
              }`}
              rows={4}
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Curation Notes
            </label>
            <textarea
              value={curationNotes}
              onChange={(e) => onFieldChange("curationNotes", e.target.value)}
              placeholder="Why is this product special? What makes it a great find?"
              className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none"
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              Share your expertise - what makes this product worth featuring?
            </p>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Supplier *
            </label>
            <Select
              value={supplierId}
              onValueChange={(value) => onFieldChange("supplierId", value)}
            >
              <SelectTrigger className={errors.supplierId ? "border-destructive" : ""}>
                <SelectValue placeholder="Select a supplier" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map((supplier) => (
                  <SelectItem key={supplier._id} value={supplier._id}>
                    <div className="flex items-center space-x-2">
                      <span>{supplier.name}</span>
                      {!supplier.isActive && (
                        <span className="text-xs text-muted-foreground">(Inactive)</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.supplierId && (
              <p className="text-sm text-destructive">{errors.supplierId}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Product Status & Inventory */}
      <Card>
        <CardHeader>
          <CardTitle>Status & Inventory</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Status
              </label>
              <Select
                value={status}
                onValueChange={(value) => onFieldChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ProductStatus.ACTIVE}>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-success"></div>
                      <span>Active</span>
                    </div>
                  </SelectItem>
                  <SelectItem value={ProductStatus.INACTIVE}>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-warning"></div>
                      <span>Inactive</span>
                    </div>
                  </SelectItem>
                  <SelectItem value={ProductStatus.ARCHIVED}>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-muted-foreground"></div>
                      <span>Archived</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Stock Count
              </label>
              <Input
                type="number"
                value={stockCount || ""}
                onChange={(e) => onFieldChange("stockCount", parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}