import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

// The schema is normally optional, but Convex Auth
// requires indexes defined on `authTables`.
// The schema provides more precise TypeScript types.
export default defineSchema({
  ...authTables,

  // Admin roles and permissions
  adminUsers: defineTable({
    userId: v.id("users"),
    role: v.union(v.literal("super_admin"), v.literal("admin"), v.literal("moderator")),
    permissions: v.array(v.string()),
    createdBy: v.id("users"),
    isActive: v.boolean(),
    lastLoginAt: v.optional(v.number()),
  }).index("by_user", ["userId"])
    .index("by_role", ["role"])
    .index("by_active", ["isActive"]),

  // Products table as per vision document
  products: defineTable({
    // Existing fields...
    title: v.string(),
    description: v.string(),
    curationNotes: v.string(),
    supplierId: v.id("suppliers"),
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),
    tags: v.array(v.string()),
    images: v.array(v.union(v.string(), v.id("_storage"))),
    imageEmbedding: v.optional(v.union(v.array(v.number()), v.null())),
    stockCount: v.number(),
    status: v.union(v.literal("active"), v.literal("inactive"), v.literal("archived")),
    createdBy: v.id("users"),
    updatedBy: v.id("users"),

    // New provider-agnostic fields
    providerData: v.object({
      source: v.string(), // 'alibaba', 'taobao', etc.
      productUrl: v.string(), // Direct link to provider product
      providerId: v.string(), // Provider's internal product ID
      lastScraped: v.number(), // Timestamp
      // Extensible field for provider-specific data
      providerSpecificData: v.optional(v.any()), // Store unique provider fields (e.g., Pinduoduo group buy metadata)
    }),

    // Complex pricing structure - base tiers
    pricingTiers: v.array(v.object({
      minQuantity: v.number(),
      maxQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      discountPercentage: v.optional(v.number()),
      // Enhanced for multi-provider compatibility
      regionCode: v.optional(v.string()), // For regional pricing (e.g., mainland vs HK)
      participantRequirement: v.optional(v.number()), // For group buy requirements
      timeConstraint: v.optional(v.object({
        startTime: v.number(),
        endTime: v.number()
      })), // For time-limited promotions
    })),

    // Product variants with pricing
    variants: v.array(v.object({
      type: v.string(), // 'color', 'size', 'material'
      name: v.string(), // 'Red', 'Large', 'Cotton'
      value: v.string(), // Actual value for ordering
      priceType: v.union(v.literal('modifier'), v.literal('absolute')), // How price is calculated
      priceModifier: v.optional(v.number()), // Additional cost (for modifier type)
      absolutePrice: v.optional(v.number()), // Fixed price (for absolute type)
      currency: v.optional(v.string()),
      availableQuantity: v.optional(v.number()),
      images: v.optional(v.array(v.string())), // Variant-specific images
    })),

    // Custom services offered
    customServices: v.array(v.object({
      name: v.string(),
      description: v.optional(v.string()),
      minQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      isRequired: v.boolean(),
    })),

    // Enhanced attributes for filtering/search
    attributes: v.array(v.object({ 
      name: v.string(), 
      value: v.any()
    })),
  }).index("by_supplier", ["supplierId"])
    .index("by_status", ["status"])
    .index("by_created_by", ["createdBy"])
    .index("by_provider_product_url", ["providerData.productUrl"]),

  // Suppliers table
  suppliers: defineTable({
    name: v.string(),
    contactInfo: v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    }),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.boolean(),
    createdBy: v.id("users"),
  }).index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"]),

  // Orders table as per vision document
  orders: defineTable({
    // Existing fields...
    userId: v.id("users"),
    items: v.array(v.object({
      productId: v.id("products"),
      quantity: v.number(),
      priceAtTime: v.number(),
      title: v.string(),
    })),
    status: v.union(
      v.literal("new"),
      v.literal("sourcing"),
      v.literal("action_required"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    trackingNumber: v.optional(v.string()),
    shippingAddress: v.object({
      name: v.string(),
      address: v.string(),
      city: v.string(),
      country: v.string(),
      postalCode: v.string(),
    }),
    communicationHistory: v.array(v.object({
      message: v.string(),
      fromAdmin: v.boolean(),
      adminUserId: v.optional(v.id("users")),
      timestamp: v.number(),
    })),
    issueResolution: v.optional(v.object({
      issue: v.string(),
      suggestedAlternatives: v.array(v.id("products")),
      resolution: v.optional(v.string()),
    })),
    totalAmount: v.number(),
    assignedTo: v.optional(v.id("users")),

    // Provider-specific order details
    providerOrderData: v.object({
      selectedVariant: v.optional(v.object({
        type: v.string(),
        name: v.string(),
        value: v.string(),
        priceType: v.string(),
        finalPrice: v.number(), // Calculated final price for this variant
      })),
      selectedQuantityTier: v.object({
        minQuantity: v.number(),
        price: v.number(),
        currency: v.string(),
      }),
      selectedCustomServices: v.array(v.object({
        name: v.string(),
        price: v.number(),
      })),
      totalProviderCost: v.number(), // What we pay to provider
      pricingBreakdown: v.object({
        basePrice: v.number(),
        variantAdjustment: v.number(),
        quantityDiscount: v.number(),
        customServicesTotal: v.number(),
        ourCommission: v.number(),
      }),
    }),
  }).index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_assigned_to", ["assignedTo"]),

  // Group buys table as per vision document
  groupBuys: defineTable({
    // Existing fields...
    productId: v.id("products"),
    targetTiers: v.array(v.object({
      quantity: v.number(),
      price: v.number(),
    })),
    currentParticipants: v.number(),
    status: v.union(v.literal("active"), v.literal("completed"), v.literal("expired")),
    startTime: v.number(),
    endTime: v.number(),
    createdBy: v.id("users"),

    // Link to provider pricing tiers
    providerPricingTiers: v.array(v.object({
      minParticipants: v.number(),
      pricePerUnit: v.number(),
      ourCommission: v.number(), // Our markup
      finalPrice: v.number(), // Price user pays
    })),
  }).index("by_product", ["productId"])
    .index("by_status", ["status"])
    .index("by_end_time", ["endTime"]),

  groupBuyParticipants: defineTable({
    groupBuyId: v.id("groupBuys"),
    userId: v.id("users"),
    quantity: v.number(),
    joinedAt: v.number(),
  }).index("by_group_buy_and_user", ["groupBuyId", "userId"])
    .index("by_group_buy", ["groupBuyId"]),

  // Remove the test numbers table
});
