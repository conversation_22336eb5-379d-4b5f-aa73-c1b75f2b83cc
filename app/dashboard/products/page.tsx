"use client";

import { useState, useRef } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Search, Edit, Trash2, Eye, Image as ImageIcon, X, Sparkles } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import VisualSearchModal from "@/components/search/VisualSearchModal";

type ProductStatus = "active" | "inactive" | "archived";
type StockFilter = "in_stock" | "low_stock" | "out_of_stock";

export default function ProductsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchMode, setSearchMode] = useState<"text" | "image">("text");

  const [searchImagePreview, setSearchImagePreview] = useState<string | null>(null);
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [stockFilter, setStockFilter] = useState<string>("all");

  const fileInputRef = useRef<HTMLInputElement>(null);
  const generateEmbedding = useAction(api.embeddings.generateImageEmbedding);

  // Get products data using advanced search
  const products = useQuery(api.products.advancedSearchProducts, {
    searchTerm: searchTerm || undefined,
    status: statusFilter === "all" ? undefined : (statusFilter as ProductStatus),
    priceRange: priceRange.min && priceRange.max ? {
      min: parseFloat(priceRange.min) || 0,
      max: parseFloat(priceRange.max) || 999999,
    } : undefined,
    stockFilter: stockFilter === "all" ? undefined : (stockFilter as StockFilter),
    limit: 50,
  });

  // Use the products directly from the advanced search
  const filteredProducts = products || [];

  const handleImageSearch = async (file: File) => {
    try {
      const imageUrl = URL.createObjectURL(file);
      await generateEmbedding({ imageUrl });

      // Here you would call the image search API with the embedding
      // For now, we'll just show the image preview
      setSearchImagePreview(imageUrl);

      // Clean up the temporary URL
      URL.revokeObjectURL(imageUrl);
    } catch (error) {
      console.error("Error processing search image:", error);
      alert("Failed to process search image. Please try again.");
    }
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      handleImageSearch(file);
    }

    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const clearImageSearch = () => {
    if (searchImagePreview) {
      URL.revokeObjectURL(searchImagePreview);
      setSearchImagePreview(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "inactive":
        return <Badge variant="secondary">Inactive</Badge>;
      case "archived":
        return <Badge variant="outline">Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>;
    } else if (stock < 10) {
      return <Badge variant="destructive">Low Stock</Badge>;
    } else if (stock < 50) {
      return <Badge variant="secondary">Medium Stock</Badge>;
    } else {
      return <Badge variant="default">In Stock</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Products
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage your product catalog and inventory
          </p>
        </div>
        <div className="flex space-x-2">
          <VisualSearchModal
            trigger={
              <Button variant="outline">
                <Sparkles className="h-4 w-4 mr-2" />
                Visual Search
              </Button>
            }
          />
          <Button asChild>
            <Link href="/dashboard/products/new">
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Link>
          </Button>
        </div>
      </div>

      {/* Advanced Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Advanced Search & Filters</CardTitle>
          <CardDescription>
            Search products by text or image, and apply advanced filters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Mode Toggle */}
          <div className="flex space-x-2">
            <Button
              variant={searchMode === "text" ? "default" : "outline"}
              size="sm"
              onClick={() => setSearchMode("text")}
            >
              <Search className="h-4 w-4 mr-2" />
              Text Search
            </Button>
            <Button
              variant={searchMode === "image" ? "default" : "outline"}
              size="sm"
              onClick={() => setSearchMode("image")}
            >
              <ImageIcon className="h-4 w-4 mr-2" />
              Image Search
            </Button>
          </div>

          {/* Search Input */}
          {searchMode === "text" ? (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search products by title, description, or tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          ) : (
            <div className="space-y-2">
              {/* Image Upload Area */}
              <div
                className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                {searchImagePreview ? (
                  <div className="relative inline-block">
                    <Image
                      src={searchImagePreview}
                      alt="Search image"
                      width={100}
                      height={100}
                      className="rounded-lg object-cover"
                    />
                    <Button
                      size="sm"
                      variant="destructive"
                      className="absolute -top-2 -right-2 h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearImageSearch();
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <>
                    <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Click to upload an image to search for similar products
                    </p>
                  </>
                )}
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageSelect}
              />
            </div>
          )}

          {/* Filter Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            {/* Stock Filter */}
            <Select value={stockFilter} onValueChange={setStockFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Stock Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Stock Levels</SelectItem>
                <SelectItem value="in_stock">In Stock (&gt;10)</SelectItem>
                <SelectItem value="low_stock">Low Stock (1-10)</SelectItem>
                <SelectItem value="out_of_stock">Out of Stock</SelectItem>
              </SelectContent>
            </Select>

            {/* Price Range */}
            <div className="flex space-x-2">
              <Input
                placeholder="Min $"
                value={priceRange.min}
                onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                type="number"
                className="w-full"
              />
              <Input
                placeholder="Max $"
                value={priceRange.max}
                onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                type="number"
                className="w-full"
              />
            </div>

            {/* Clear Filters */}
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setStatusFilter("all");
                setStockFilter("all");
                setPriceRange({ min: "", max: "" });
                clearImageSearch();
                setSearchMode("text");
              }}
            >
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Products ({filteredProducts.length})
          </CardTitle>
          <CardDescription>
            {products ? `Showing ${filteredProducts.length} products` : "Loading products..."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Tags</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      {!products || products.length === 0 ? (
                        <div>
                          <p className="text-gray-500 dark:text-gray-400 mb-2">
                            No products found
                          </p>
                          <Button asChild variant="outline">
                            <Link href="/dashboard/products/new">
                              <Plus className="h-4 w-4 mr-2" />
                              Add your first product
                            </Link>
                          </Button>
                        </div>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400">
                          No products match your search criteria
                        </p>
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProducts.map((product) => (
                    <TableRow key={product._id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          {product.images.length > 0 && (
                            <Image
                              src={product.images[0]}
                              alt={product.title}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded-md object-cover"
                            />
                          )}
                          <div>
                            <div className="font-medium">{product.title}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                              {product.description}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">${product.finalPrice}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            ¥{product.priceInYuan} + ${product.serviceFee}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{product.stockCount} units</div>
                          {getStockBadge(product.stockCount)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(product.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {product.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {product.tags.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{product.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/dashboard/products/${product._id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/dashboard/products/${product._id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
