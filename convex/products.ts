import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Query to get all products with pagination
export const getProducts = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    supplierId: v.optional(v.id("suppliers")),
  },
  handler: async (ctx, { paginationOpts, status, supplierId }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    // Build query with proper initialization
    let query;

    // Apply filters
    if (status) {
      query = ctx.db.query("products").withIndex("by_status", (q) => q.eq("status", status));
    } else if (supplierId) {
      query = ctx.db.query("products").withIndex("by_supplier", (q) => q.eq("supplierId", supplierId));
    } else {
      query = ctx.db.query("products");
    }

    // Apply pagination
    if (paginationOpts) {
      const results = await query.paginate(paginationOpts);
      
      // Get supplier info for each product
      const productsWithSuppliers = await Promise.all(
        results.page.map(async (product) => {
          const supplier = await ctx.db.get(product.supplierId);
          return {
            ...product,
            supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
          };
        })
      );

      return {
        ...results,
        page: productsWithSuppliers,
      };
    }

    // Get all products without pagination
    const products = await query.collect();
    const productsWithSuppliers = await Promise.all(
      products.map(async (product) => {
        const supplier = await ctx.db.get(product.supplierId);
        return {
          ...product,
          supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
        };
      })
    );

    return productsWithSuppliers;
  },
});

// Query to get a single product by ID
export const getProduct = query({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    const supplier = await ctx.db.get(product.supplierId);
    const createdBy = await ctx.db.get(product.createdBy);
    const updatedBy = await ctx.db.get(product.updatedBy);

    return {
      ...product,
      supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
      createdByUser: createdBy ? { name: createdBy.name, email: createdBy.email } : null,
      updatedByUser: updatedBy ? { name: updatedBy.name, email: updatedBy.email } : null,
    };
  },
});

// Mutation to create a new product
export const createProduct = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    curationNotes: v.string(),
    supplierId: v.id("suppliers"),
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),
    tags: v.array(v.string()),
    images: v.array(v.union(v.string(), v.id("_storage"))),
    stockCount: v.number(),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    providerData: v.object({
      source: v.string(),
      productUrl: v.string(),
      providerId: v.string(),
      lastScraped: v.number(),
      providerSpecificData: v.optional(v.any()),
    }),
    pricingTiers: v.array(v.object({
      minQuantity: v.number(),
      maxQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      discountPercentage: v.optional(v.number()),
    })),
    variants: v.array(v.object({
      type: v.string(),
      name: v.string(),
      value: v.string(),
      priceType: v.union(v.literal('modifier'), v.literal('absolute')),
      priceModifier: v.optional(v.number()),
      absolutePrice: v.optional(v.number()),
      currency: v.optional(v.string()),
      availableQuantity: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
    })),
    customServices: v.array(v.object({
      name: v.string(),
      description: v.optional(v.string()),
      minQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      isRequired: v.boolean(),
    })),
    attributes: v.array(v.object({
      name: v.string(),
      value: v.any(),
    })),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_CREATE);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate supplier exists
    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Validate input
    if (args.priceInYuan <= 0 || args.finalPrice <= 0) {
      throw new Error("Prices must be positive");
    }

    if (args.stockCount < 0) {
      throw new Error("Stock count cannot be negative");
    }

    if (!args.title.trim() || !args.description.trim()) {
      throw new Error("Title and description are required");
    }

    const productId = await ctx.db.insert("products", {
      ...args,
      status: args.status || "active",
      createdBy: userId,
      updatedBy: userId,
    });

    return productId;
  },
});

// Mutation to update a product
export const updateProduct = mutation({
  args: {
    id: v.id("products"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    curationNotes: v.optional(v.string()),
    supplierId: v.optional(v.id("suppliers")),
    priceInYuan: v.optional(v.number()),
    serviceFee: v.optional(v.number()),
    finalPrice: v.optional(v.number()),
    tags: v.optional(v.array(v.string())),
    images: v.optional(v.array(v.union(v.string(), v.id("_storage")))),
    stockCount: v.optional(v.number()),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    providerData: v.optional(v.object({
      source: v.string(),
      productUrl: v.string(),
      providerId: v.string(),
      lastScraped: v.number(),
      providerSpecificData: v.optional(v.any()),
    })),
    pricingTiers: v.optional(v.array(v.object({
      minQuantity: v.number(),
      maxQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      discountPercentage: v.optional(v.number()),
    }))),
    variants: v.optional(v.array(v.object({
      type: v.string(),
      name: v.string(),
      value: v.string(),
      priceType: v.union(v.literal('modifier'), v.literal('absolute')),
      priceModifier: v.optional(v.number()),
      absolutePrice: v.optional(v.number()),
      currency: v.optional(v.string()),
      availableQuantity: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
    }))),
    customServices: v.optional(v.array(v.object({
      name: v.string(),
      description: v.optional(v.string()),
      minQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      isRequired: v.boolean(),
    }))),
    attributes: v.optional(v.array(v.object({
      name: v.string(),
      value: v.any(),
    }))),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_EDIT);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    // Validate supplier if being updated
    if (updates.supplierId) {
      const supplier = await ctx.db.get(updates.supplierId);
      if (!supplier) {
        throw new Error("Supplier not found");
      }
    }

    // Validate prices if being updated
    if (updates.priceInYuan !== undefined && updates.priceInYuan <= 0) {
      throw new Error("Price in Yuan must be positive");
    }

    if (updates.finalPrice !== undefined && updates.finalPrice <= 0) {
      throw new Error("Final price must be positive");
    }

    if (updates.stockCount !== undefined && updates.stockCount < 0) {
      throw new Error("Stock count cannot be negative");
    }

    await ctx.db.patch(id, {
      ...updates,
      updatedBy: userId,
    });

    return { success: true };
  },
});

// Mutation to delete a product (soft delete by setting status to archived)
export const deleteProduct = mutation({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_DELETE);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    await ctx.db.patch(id, {
      status: "archived",
      updatedBy: userId,
    });

    return { success: true };
  },
});



// Query to get product statistics
export const getProductStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const products = await ctx.db.query("products").collect();

    const stats = {
      total: products.length,
      active: products.filter(p => p.status === "active").length,
      inactive: products.filter(p => p.status === "inactive").length,
      archived: products.filter(p => p.status === "archived").length,
      lowStock: products.filter(p => p.stockCount < 10 && p.status === "active").length,
      outOfStock: products.filter(p => p.stockCount === 0 && p.status === "active").length,
    };

    return stats;
  },
});



// Mutation to update product with generated embedding
export const updateProductEmbedding = mutation({
  args: {
    productId: v.id("products"),
    embedding: v.array(v.number()),
  },
  handler: async (ctx, { productId, embedding }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_EDIT);

    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    await ctx.db.patch(productId, {
      imageEmbedding: embedding,
    });

    return { success: true };
  },
});

// Query to get product images with URLs for embedding generation
export const getProductImagesForEmbedding = query({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, { productId }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    const imageUrls = [];
    for (const imageId of product.images) {
      try {
        if (typeof imageId !== "string") {
          // It's a storage ID, get the URL
          const imageUrl = await ctx.storage.getUrl(imageId);
          if (imageUrl) {
            imageUrls.push(imageUrl);
          }
        } else {
          // It's already a URL
          imageUrls.push(imageId);
        }
      } catch (error) {
        console.error("Error getting image URL:", error);
      }
    }

    return {
      productId,
      imageUrls,
      hasEmbedding: !!product.imageEmbedding,
    };
  },
});

// Advanced search query with text and embedding support
export const advancedSearchProducts = query({
  args: {
    searchTerm: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    supplierId: v.optional(v.id("suppliers")),
    priceRange: v.optional(v.object({
      min: v.number(),
      max: v.number(),
    })),
    stockFilter: v.optional(v.union(
      v.literal("in_stock"),
      v.literal("low_stock"),
      v.literal("out_of_stock")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    let query = ctx.db.query("products");

    // Apply status filter
    if (args.status) {
      query = query.filter(q => q.eq(q.field("status"), args.status));
    }

    // Apply supplier filter
    if (args.supplierId) {
      query = query.filter(q => q.eq(q.field("supplierId"), args.supplierId));
    }

    let products = await query.collect();

    // Apply text search filter
    if (args.searchTerm && args.searchTerm.trim()) {
      const searchLower = args.searchTerm.toLowerCase();
      products = products.filter(product =>
        product.title.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower) ||
        product.curationNotes.toLowerCase().includes(searchLower) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply tag filter
    if (args.tags && args.tags.length > 0) {
      const tagsToFilter = args.tags;
      products = products.filter(product =>
        tagsToFilter.some(tag => product.tags.includes(tag))
      );
    }

    // Apply price range filter
    if (args.priceRange) {
      const priceRange = args.priceRange;
      products = products.filter(product =>
        product.finalPrice >= priceRange.min &&
        product.finalPrice <= priceRange.max
      );
    }

    // Apply stock filter
    if (args.stockFilter) {
      switch (args.stockFilter) {
        case "in_stock":
          products = products.filter(p => p.stockCount > 10);
          break;
        case "low_stock":
          products = products.filter(p => p.stockCount > 0 && p.stockCount <= 10);
          break;
        case "out_of_stock":
          products = products.filter(p => p.stockCount === 0);
          break;
      }
    }

    // Apply limit
    if (args.limit) {
      products = products.slice(0, args.limit);
    }

    // Enrich with supplier information
    const enrichedProducts = await Promise.all(
      products.map(async (product) => {
        const supplier = await ctx.db.get(product.supplierId);
        return {
          ...product,
          supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
        };
      })
    );

    return enrichedProducts;
  },
});

// Search products by image similarity using embeddings
export const searchProductsByImage = query({
  args: {
    targetEmbedding: v.array(v.number()),
    limit: v.optional(v.number()),
    threshold: v.optional(v.number()),
  },
  handler: async (ctx, { targetEmbedding, limit = 10, threshold = 0.7 }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    // Get all products with embeddings
    const products = await ctx.db
      .query("products")
      .filter(q => q.neq(q.field("imageEmbedding"), undefined))
      .collect();

    // Calculate similarity scores
    const productsWithSimilarity = products
      .map(product => {
        if (!product.imageEmbedding) return null;

        // Calculate cosine similarity
        const similarity = calculateCosineSimilarity(targetEmbedding, product.imageEmbedding);

        return {
          ...product,
          similarity,
        };
      })
      .filter((product): product is NonNullable<typeof product> =>
        product !== null && product.similarity >= threshold
      )
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit);

    // Enrich with supplier information
    const enrichedProducts = await Promise.all(
      productsWithSimilarity.map(async (product) => {
        const supplier = await ctx.db.get(product.supplierId);
        return {
          ...product,
          supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
        };
      })
    );

    return enrichedProducts;
  },
});

// Helper function to calculate cosine similarity
function calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
  if (embedding1.length !== embedding2.length) {
    return 0;
  }

  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i];
    norm1 += embedding1[i] * embedding1[i];
    norm2 += embedding2[i] * embedding2[i];
  }

  const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);

  if (magnitude === 0) {
    return 0;
  }

  return dotProduct / magnitude;
}


// Query to get all unique tags from products
export const getUniqueTags = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const products = await ctx.db.query("products").collect();
    const allTags = new Set<string>();

    products.forEach(product => {
      product.tags.forEach(tag => allTags.add(tag));
    });

    return Array.from(allTags);
  },
});