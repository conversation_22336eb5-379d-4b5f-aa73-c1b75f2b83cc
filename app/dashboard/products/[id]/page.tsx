"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/products/PageHeader";
import { ProductInfoForm } from "@/components/products/ProductInfoForm";
import { PriceCalculator } from "@/components/products/PriceCalculator";
import { TagManager } from "@/components/products/TagManager";
import { ImageUploadArea } from "@/components/products/ImageUploadArea";
import { PricingTiersForm } from "@/components/products/PricingTiersForm";
import { VariantsForm } from "@/components/products/VariantsForm";
import { CustomServicesForm } from "@/components/products/CustomServicesForm";
import { AttributesForm } from "@/components/products/AttributesForm";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { ProductStatus } from "@/app/productRedesignMockData";

// Enums and types
enum ImageUploadState {
  PENDING = "pending",
  UPLOADING = "uploading",
  UPLOADED = "uploaded",
  ERROR = "error",
}

interface ImageFile {
  file?: File;
  preview: string;
  uploaded: boolean;
  storageId?: Id<"_storage">;
  uploadState: ImageUploadState;
}

interface PricingTier {
  minQuantity: number;
  maxQuantity?: number;
  price: number;
  currency: string;
  discountPercentage?: number;
}

interface Variant {
  type: string;
  name: string;
  value: string;
  priceType: 'modifier' | 'absolute';
  priceModifier?: number;
  absolutePrice?: number;
  currency?: string;
  availableQuantity?: number;
  images?: string[];
}

interface CustomService {
  name: string;
  description?: string;
  minQuantity?: number;
  price: number;
  currency: string;
  isRequired: boolean;
}

interface Attribute {
  name: string;
  value: string;
}

// Use a partial form data for updates
interface FormData {
  title?: string;
  description?: string;
  curationNotes?: string;
  supplierId?: Id<"suppliers">;
  priceInYuan?: number;
  serviceFee?: number;
  finalPrice?: number;
  stockCount?: number;
  status?: ProductStatus;
  tags?: string[];
  images?: (string | Id<"_storage">)[];
  pricingTiers?: PricingTier[];
  variants?: Variant[];
  customServices?: CustomService[];
  attributes?: Attribute[];
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as Id<"products">;

  const product = useQuery(api.products.getProduct, { id });
  const suppliers = useQuery(api.suppliers.getSuppliers, { activeOnly: true });
  const updateProduct = useMutation(api.products.updateProduct);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);

  const [formData, setFormData] = useState<Partial<FormData>>({});
  const [imageFiles, setImageFiles] = useState<ImageFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const tagSuggestions = [
    "electronics", "premium", "wireless", "bluetooth", "audio", "gaming", 
    "home", "kitchen", "outdoor", "fitness", "tech", "gadgets", "accessories"
  ];

  useEffect(() => {
    if (product) {
      setFormData({
        title: product.title,
        description: product.description,
        curationNotes: product.curationNotes,
        supplierId: product.supplierId,
        priceInYuan: product.priceInYuan,
        serviceFee: product.serviceFee,
        finalPrice: product.finalPrice,
        stockCount: product.stockCount,
        status: product.status as ProductStatus,
        tags: product.tags,
        images: product.images,
        pricingTiers: product.pricingTiers,
        variants: product.variants,
        customServices: product.customServices,
        attributes: product.attributes,
      });

      const fetchImageFiles = async () => {
        const files = await Promise.all(product.images.map(async (image) => {
          let url = image;
          if (image.startsWith("storage_")) {
            url = "/placeholder.svg"; // Placeholder
          }
          return {
            preview: url,
            uploaded: true,
            storageId: image as Id<"_storage">,
            uploadState: ImageUploadState.UPLOADED,
          };
        }));
        setImageFiles(files);
      };
      fetchImageFiles();
    }
  }, [product]);

  const handleFieldChange = (field: string, value: unknown) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    if (!formData.title?.trim()) newErrors.title = "Product title is required";
    if (!formData.description?.trim()) newErrors.description = "Product description is required";
    if (!formData.supplierId) newErrors.supplierId = "Please select a supplier";
    if (formData.priceInYuan !== undefined && formData.priceInYuan <= 0) newErrors.priceInYuan = "Price must be greater than 0";
    if (imageFiles.some(img => !img.uploaded && img.file)) newErrors.images = "Please wait for all images to finish uploading";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const uploadImage = async (index: number): Promise<void> => {
    const imageFile = imageFiles[index];
    if (!imageFile || imageFile.uploaded || !imageFile.file) return;

    setImageFiles(prev => prev.map((img, i) => i === index ? { ...img, uploadState: ImageUploadState.UPLOADING } : img));

    try {
      const uploadUrl = await generateUploadUrl();
      const response = await fetch(uploadUrl, { method: "POST", headers: { "Content-Type": imageFile.file.type }, body: imageFile.file });
      const { storageId } = await response.json();
      
      setImageFiles(prev => prev.map((img, i) => i === index ? { ...img, uploaded: true, storageId, uploadState: ImageUploadState.UPLOADED } : img));
      setFormData(prev => ({ ...prev, images: [...(prev.images || []), storageId] }));

    } catch (error) {
      console.error("Error uploading image:", error);
      setImageFiles(prev => prev.map((img, i) => i === index ? { ...img, uploadState: ImageUploadState.ERROR } : img));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      await updateProduct({ id, ...formData });
      imageFiles.forEach(img => { if (img.file) URL.revokeObjectURL(img.preview); });
      router.push("/dashboard/products");
    } catch (error) {
      console.error("Error updating product:", error);
      setErrors({ submit: "Failed to update product. Please try again." });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (product === undefined || suppliers === undefined) {
    return <div className="flex items-center justify-center min-h-screen"><Loader2 className="h-12 w-12 animate-spin" /></div>;
  }

  if (product === null) {
    return <div className="flex flex-col items-center justify-center min-h-screen"><h2 className="text-2xl font-bold mb-4">Product Not Found</h2><p className="text-muted-foreground mb-8">The product you are looking for does not exist.</p><Button asChild><Link href="/dashboard/products">Back to Products</Link></Button></div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8 space-y-8">
        <PageHeader title="Edit Product" description="Update the details of your product" backLink="/dashboard/products" backLabel="Back to Products" />
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <ProductInfoForm
                title={formData.title || ""}
                description={formData.description || ""}
                curationNotes={formData.curationNotes || ""}
                supplierId={formData.supplierId || ""}
                status={formData.status || ProductStatus.INACTIVE}
                stockCount={formData.stockCount || 0}
                suppliers={suppliers || []}
                onFieldChange={handleFieldChange}
                errors={errors}
              />
              <PriceCalculator priceInYuan={formData.priceInYuan || 0} serviceFee={formData.serviceFee || 0} onPriceChange={handleFieldChange} />
              <PricingTiersForm pricingTiers={formData.pricingTiers || []} onPricingTiersChange={(tiers) => handleFieldChange("pricingTiers", tiers)} />
              <VariantsForm variants={formData.variants || []} onVariantsChange={(variants) => handleFieldChange("variants", variants)} />
              <CustomServicesForm customServices={formData.customServices || []} onCustomServicesChange={(services) => handleFieldChange("customServices", services)} />
              <AttributesForm attributes={formData.attributes || []} onAttributesChange={(attributes) => handleFieldChange("attributes", attributes)} />
              <TagManager tags={formData.tags || []} onTagsChange={(tags) => handleFieldChange("tags", tags)} suggestions={tagSuggestions} />
            </div>
            <div className="space-y-8">
              <ImageUploadArea imageFiles={imageFiles} onImageFilesChange={setImageFiles} onUploadImage={uploadImage} maxImages={10} />
            </div>
          </div>
          <div className="flex justify-end space-x-4 pt-8 border-t">
            <Button type="button" variant="outline" asChild><Link href="/dashboard/products">Cancel</Link></Button>
            <Button type="submit" disabled={isSubmitting} className="min-w-32">
              {isSubmitting ? <><Loader2 className="h-4 w-4 mr-2 animate-spin" />Saving...</> : "Save Changes"}
            </Button>
          </div>
          {errors.submit && <div className="text-center"><p className="text-sm text-destructive">{errors.submit}</p></div>}
        </form>
      </div>
    </div>
  );
}
